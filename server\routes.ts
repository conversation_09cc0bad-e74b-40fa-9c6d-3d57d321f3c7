import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import path from "path";
import fs from "fs";
import express from "express";
import { storage } from "./storage";
import { insertProcessedImageSchema, insertNewsletterSchema } from "@shared/schema";
import { z } from "zod";

// Supported image formats
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/tiff',
  'image/tif',
  'image/svg+xml'
];

const SUPPORTED_IMAGE_EXTENSIONS = [
  '.jpg',
  '.jpeg',
  '.png',
  '.gif',
  '.webp',
  '.bmp',
  '.tiff',
  '.tif',
  '.svg'
];

const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    console.log('File upload attempt:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // Check MIME type
    const isSupportedMimeType = SUPPORTED_IMAGE_TYPES.includes(file.mimetype.toLowerCase());

    // Check file extension as fallback
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const isSupportedExtension = SUPPORTED_IMAGE_EXTENSIONS.includes(fileExtension);

    // Also check if mimetype starts with 'image/' for broader compatibility
    const isImageMimeType = file.mimetype.startsWith('image/');

    if (isSupportedMimeType || (isImageMimeType && isSupportedExtension)) {
      console.log('File accepted:', file.originalname);
      cb(null, true);
    } else {
      console.log('File rejected:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        extension: fileExtension
      });
      cb(new Error(`Unsupported file type. Supported formats: ${SUPPORTED_IMAGE_EXTENSIONS.join(', ')}`));
    }
  },
});

// Function to simulate background removal (for demo purposes)
async function simulateBackgroundRemoval(imageId: number, originalPath: string, filename: string) {
  try {
    console.log(`Simulating background removal for image ${imageId}`);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const processedPath = `processed_${filename}`;
    const outputPath = path.join('uploads', processedPath);

    // Copy the original file as "processed" (for demo purposes)
    fs.copyFileSync(originalPath, outputPath);

    // Update status to completed
    await storage.updateProcessedImageStatus(imageId, 'completed', processedPath);
    console.log(`Background removal simulation completed for image ${imageId}`);
  } catch (error) {
    console.error('Simulation error:', error);
    await storage.updateProcessedImageStatus(imageId, 'failed');
  }
}

// Function to process image with remove.bg API
async function processImageWithRemoveBg(imageId: number, originalPath: string, filename: string) {
  try {
    const apiKey = process.env.REMOVE_BG_API_KEY;

    // If no API key is configured, use simulation mode
    if (!apiKey) {
      console.log('No remove.bg API key configured, using simulation mode');
      return simulateBackgroundRemoval(imageId, originalPath, filename);
    }

    console.log(`Processing image ${imageId} with remove.bg API`);

    // Create FormData for the API request
    const FormData = await import('form-data');
    const form = new FormData.default();

    // Add image file and parameters
    form.append('image_file', fs.createReadStream(originalPath));
    form.append('size', 'auto');
    form.append('format', 'png'); // Ensure PNG format for transparency

    const processedPath = `processed_${filename}`;
    const outputPath = path.join('uploads', processedPath);

    // Make the API request
    const https = await import('https');

    const options = {
      method: 'POST',
      hostname: 'api.remove.bg',
      path: '/v1.0/removebg',
      headers: {
        'X-Api-Key': apiKey,
        ...form.getHeaders(),
      },
    };

    const req = https.request(options, (res) => {
      console.log(`Remove.bg API response status: ${res.statusCode}`);

      if (res.statusCode === 200) {
        // Success - save the processed image
        const fileStream = fs.createWriteStream(outputPath);
        res.pipe(fileStream);

        fileStream.on('finish', async () => {
          try {
            await storage.updateProcessedImageStatus(imageId, 'completed', processedPath);
            console.log(`Background removal completed for image ${imageId}`);
          } catch (error) {
            console.error('Error updating status to completed:', error);
          }
        });

        fileStream.on('error', async (error) => {
          console.error('File write error:', error);
          try {
            await storage.updateProcessedImageStatus(imageId, 'failed');
          } catch (updateError) {
            console.error('Error updating status to failed:', updateError);
          }
        });
      } else {
        // API error - log response and update status
        let errorData = '';
        res.on('data', (chunk) => {
          errorData += chunk;
        });

        res.on('end', async () => {
          console.error(`Remove.bg API error ${res.statusCode}:`, errorData);
          try {
            await storage.updateProcessedImageStatus(imageId, 'failed');
          } catch (updateError) {
            console.error('Error updating status to failed:', updateError);
          }
        });
      }
    });

    req.on('error', async (error) => {
      console.error('Request error:', error);
      try {
        await storage.updateProcessedImageStatus(imageId, 'failed');
      } catch (updateError) {
        console.error('Error updating status to failed:', updateError);
      }
    });

    // Send the form data
    form.pipe(req);

  } catch (error) {
    console.error('Processing error:', error);
    try {
      await storage.updateProcessedImageStatus(imageId, 'failed');
    } catch (updateError) {
      console.error('Error updating status to failed:', updateError);
    }
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Ensure uploads directory exists
  if (!fs.existsSync('uploads')) {
    fs.mkdirSync('uploads', { recursive: true });
  }

  // Serve test files
  app.use(express.static('.', {
    index: false,
    setHeaders: (res, path) => {
      if (path.endsWith('.html')) {
        res.setHeader('Content-Type', 'text/html');
      }
    }
  }));

  // Serve uploaded files with proper headers
  app.use('/uploads', express.static('uploads', {
    setHeaders: (res, filepath) => {
      if (filepath.match(/\.(jpg|jpeg|png|gif|webp|bmp|tiff|tif|svg)$/i) || filepath.includes('processed_')) {
        const ext = path.extname(filepath).toLowerCase();
        switch (ext) {
          case '.png':
            res.setHeader('Content-Type', 'image/png');
            break;
          case '.gif':
            res.setHeader('Content-Type', 'image/gif');
            break;
          case '.webp':
            res.setHeader('Content-Type', 'image/webp');
            break;
          case '.bmp':
            res.setHeader('Content-Type', 'image/bmp');
            break;
          case '.tiff':
          case '.tif':
            res.setHeader('Content-Type', 'image/tiff');
            break;
          case '.svg':
            res.setHeader('Content-Type', 'image/svg+xml');
            break;
          case '.jpg':
          case '.jpeg':
          default:
            res.setHeader('Content-Type', 'image/jpeg');
            break;
        }
      }
    }
  }));

  // Upload and process image
  app.post('/api/upload', upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No image file provided' });
      }

      const { originalname, filename, path: filepath } = req.file;
      
      // Create processed image record
      const imageData = {
        userId: null, // Anonymous upload
        originalFilename: originalname,
        originalPath: filepath,
        processedPath: '', // Will be updated after processing
        processingStatus: 'pending',
      };

      const processedImage = await storage.createProcessedImage(imageData);

      // Process the image with remove.bg API
      processImageWithRemoveBg(processedImage.id, filepath, filename);

      res.json({
        id: processedImage.id,
        status: 'pending',
        message: 'Image uploaded successfully. Processing started.',
      });
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ message: 'Upload failed' });
    }
  });

  // Check processing status
  app.get('/api/process/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);

      if (!processedImage) {
        return res.status(404).json({ message: 'Image not found' });
      }

      // Check if we're in simulation mode
      const isSimulationMode = !process.env.REMOVE_BG_API_KEY;

      res.json({
        id: processedImage.id,
        status: processedImage.processingStatus,
        originalPath: processedImage.originalPath,
        processedPath: processedImage.processedPath,
        isSimulationMode: isSimulationMode
      });
    } catch (error) {
      console.error('Status check error:', error);
      res.status(500).json({ message: 'Failed to check status' });
    }
  });

  // Download processed image
  app.get('/api/download/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);
      
      if (!processedImage || processedImage.processingStatus !== 'completed') {
        return res.status(404).json({ message: 'Processed image not found' });
      }

      const filePath = path.join('uploads', processedImage.processedPath);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ message: 'File not found' });
      }

      res.download(filePath, `processed_${processedImage.originalFilename}`);
    } catch (error) {
      console.error('Download error:', error);
      res.status(500).json({ message: 'Download failed' });
    }
  });

  // Newsletter subscription
  app.post('/api/newsletter', async (req, res) => {
    try {
      const validatedData = insertNewsletterSchema.parse(req.body);
      
      // Check if already subscribed
      const existing = await storage.getNewsletterSubscriber(validatedData.email);
      if (existing) {
        if (existing.subscribed) {
          return res.status(400).json({ message: 'Already subscribed' });
        } else {
          // Reactivate subscription
          await storage.updateNewsletterSubscription(validatedData.email, true);
          return res.json({ message: 'Subscription reactivated' });
        }
      }

      await storage.createNewsletterSubscriber(validatedData);
      res.json({ message: 'Successfully subscribed to newsletter' });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid data', errors: error.errors });
      }
      console.error('Newsletter subscription error:', error);
      res.status(500).json({ message: 'Subscription failed' });
    }
  });

  // Unsubscribe from newsletter
  app.post('/api/newsletter/unsubscribe', async (req, res) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      const updated = await storage.updateNewsletterSubscription(email, false);
      
      if (!updated) {
        return res.status(404).json({ message: 'Email not found' });
      }

      res.json({ message: 'Successfully unsubscribed' });
    } catch (error) {
      console.error('Unsubscribe error:', error);
      res.status(500).json({ message: 'Unsubscribe failed' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
